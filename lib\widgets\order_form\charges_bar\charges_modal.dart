import 'package:flutter/material.dart';
import 'package:phoenix/services/comissions_charges_service.dart';
import 'package:phoenix/utils/theme_constants.dart';

class CalculatedChargesModal {
  final double brokerageCharge;
  final double sttCttCharge;
  final double transactionCharge;
  final double ipftCharge;
  final double sebiCharge;
  final double stampCharge;
  final double gstCharge;
  final double exchangeCharge;
  final double finalCharges;

  CalculatedChargesModal({
    required this.brokerageCharge,
    required this.sttCttCharge,
    required this.transactionCharge,
    required this.ipftCharge,
    required this.sebiCharge,
    required this.stampCharge,
    required this.gstCharge,
    required this.exchangeCharge,
    required this.finalCharges,
  });
}

class ChargesModal extends StatelessWidget {
  final CalculatedCharges charges;
  final double? requiredMargin;
  final double? availableMargin;

  const ChargesModal({
    Key? key,
    required this.charges,
    this.requiredMargin,
    this.availableMargin,
  }) : super(key: key);

  String formatCurrency(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withOpacity(0.90), // slate-800

      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        constraints: const BoxConstraints(maxWidth: 400),
        
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Text(
                  'Charges and taxes',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                // const SizedBox(width: 8),
                // Icon(
                //   Icons.info_outline,
                //   color: Colors.blue[400],
                //   size: 18,
                // ),
              ],
            ),
            const SizedBox(height: 20),

            // Charges List
            _buildChargeItem('Brokerage', charges.brokerageCharge),
            _buildChargeItem('SEBI turnover fee', charges.sebiCharge),
            //_buildChargeItem('Exchange turnover fee', charges.exchangeCharge),
            _buildChargeItem('Stamp duty', charges.stampCharge),
            _buildChargeItem('Transaction charges', charges.transactionCharge),
            _buildChargeItem('IPFT Charge', charges.ipftCharge),
            _buildChargeItem('Transaction tax (CTT/STT)', charges.sttCttCharge),
            _buildChargeItem('GST', charges.gstCharge),

            // Divider
            Container(
              margin: const EdgeInsets.symmetric(vertical: 16),
              height: 1,
              color: ThemeConstants.zenWhite, // slate-600
            ),

            // Total Charges
            _buildChargeItem(
              'Total charges',
              charges.finalCharges,
              isTotal: true,
            ),

            // Margin Information (if provided)
            if (requiredMargin != null) ...[
              const SizedBox(height: 8),
              _buildChargeItem('Required margin', requiredMargin!),
            ],
            if (availableMargin != null) ...[
              const SizedBox(height: 8),
              _buildChargeItem('Available margin', availableMargin!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildChargeItem(String label, double value, {bool isTotal = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: isTotal ? Colors.white : const Color(0xFFCBD5E1), // slate-300
              fontSize: 14,
              fontWeight: isTotal ? FontWeight.w500 : FontWeight.normal,
            ),
          ),
          Text(
            formatCurrency(value),
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Static method to show the modal
  static Future<void> show(
    BuildContext context, {
    required CalculatedCharges charges,
    double? requiredMargin,
    double? availableMargin,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return ChargesModal(
          charges: charges,
          requiredMargin: requiredMargin,
          availableMargin: availableMargin,
        );
      },
    );
  }
}

import 'package:phoenix/features/commissions_data_map/model/broker_charge_model.dart';
import 'package:phoenix/features/commissions_data_map/model/exchange_charge_model.dart';


class CommissionChargesService {
  static CalculatedCharges calculateCharges({
    required double turnover,
    required BrokerChargeModel brokerModel,
    required ExchangeChargeModel exchangeModel,
  }) {
    // Calculate brokerage charge
    double brokerageCharge;

    if (brokerModel.flatCharges > 0.0) {
      brokerageCharge = brokerModel.flatCharges;
    } else {
      double charge = turnover * brokerModel.brokerageCharges;

      if (brokerModel.threshold > 0.0) {
        brokerageCharge = charge < brokerModel.threshold ? charge : brokerModel.threshold;
      } else {
        brokerageCharge = charge;
      }
    }

    // Exchange charges
    double sttCttCharge = turnover * exchangeModel.sttCtt;
    double transactionCharge = turnover * exchangeModel.transactionCharges;
    double ipftCharge = turnover * exchangeModel.ipft;
    double sebiCharge = turnover * exchangeModel.sebi;
    double stampCharge = turnover * exchangeModel.stampCharges;

    double gstCharge = (brokerageCharge + sebiCharge + transactionCharge) * exchangeModel.gst;

    double exchangeCharge = sttCttCharge + transactionCharge + sebiCharge + stampCharge + ipftCharge + gstCharge;

    double finalCharges = brokerageCharge + exchangeCharge;

    return CalculatedCharges(
      brokerageCharge: brokerageCharge,
      sttCttCharge: sttCttCharge,
      transactionCharge: transactionCharge,
      ipftCharge: ipftCharge,
      sebiCharge: sebiCharge,
      stampCharge: stampCharge,
      gstCharge: gstCharge,
      exchangeCharge: exchangeCharge,
      finalCharges: finalCharges,
    );
  }
}

class CalculatedCharges {
  final double brokerageCharge;
  final double sttCttCharge;
  final double transactionCharge;
  final double ipftCharge;
  final double sebiCharge;
  final double stampCharge;
  final double gstCharge;
  final double exchangeCharge;
  final double finalCharges;

  CalculatedCharges({
    required this.brokerageCharge,
    required this.sttCttCharge,
    required this.transactionCharge,
    required this.ipftCharge,
    required this.sebiCharge,
    required this.stampCharge,
    required this.gstCharge,
    required this.exchangeCharge,
    required this.finalCharges,
  });

  @override
  String toString() {
    return 'CalculatedCharges(brokerageCharge: $brokerageCharge, sttCttCharge: $sttCttCharge, transactionCharge: $transactionCharge, ipftCharge: $ipftCharge, sebiCharge: $sebiCharge, stampCharge: $stampCharge, gstCharge: $gstCharge, exchangeCharge: $exchangeCharge, finalCharges: $finalCharges)';
  }

}


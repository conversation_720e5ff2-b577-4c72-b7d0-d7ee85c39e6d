import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/watchlist/model/watchlist_model.dart' as watchlist_model;
import 'package:phoenix/services/security_cache_service.dart';
import 'package:phoenix/services/security_list_search_service.dart';
import 'package:phoenix/services/watchlist_service.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/order_form/security_list_dropdown/security_list_text_formatter.dart';
import 'package:phoenix/widgets/search/custom_search_bar.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/widgets/toast/toast_utils.dart';
import 'package:phoenix/widgets/toast/custom_toast.dart';

enum SecurityType { equity, fno }

class AddSecurityScreen extends StatefulWidget {
  final int watchlistIndex;

  const AddSecurityScreen({super.key, required this.watchlistIndex});

  @override
  State<AddSecurityScreen> createState() => _AddSecurityScreenState();
}

class _AddSecurityScreenState extends State<AddSecurityScreen> {
  final WatchlistService _watchlistService = WatchlistService();
  final TextEditingController _searchController = TextEditingController();
  final Debouncer _debouncer = Debouncer(milliseconds: 500);
  List<SecurityModel> _searchResults = [];
  watchlist_model.Watchlist? _currentWatchlist;
  SecurityType _selectedSecurityType = SecurityType.equity;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    
    // Check if security list is already loaded, if not ensure it's loaded
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!SecurityCacheService.isSecurityListReady(context)) {
        debugPrint('📋 Security list not ready, ensuring it\'s loaded');
        await SecurityCacheService.ensureSecurityListLoaded(context);
      } else {
        debugPrint('📋 Security list already ready, using cached data');
      }
    });
    
    // Initialize watchlist data
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _refreshWatchlistData();
    });
  }

  void _onSearchChanged() {
    _debouncer.run(() {
      _performSearch();
    });
  }

  void _performSearch() {
    SecurityListSearchService.optimizedSearch(
      _searchController.text,
      context,
      _selectedSecurityType == SecurityType.equity ? 'equity' : 'fno',
      () {
        if (mounted) {
          setState(() {
            _searchResults = [];
          });
        }
      },
      (results) {
        if (mounted) {
          setState(() {
            _searchResults = results;
          });
        }
      },
    );
  }

  void _addSecurity(SecurityModel security) async {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      try {
        // Check if security already exists in watchlist
        if (_isSecurityInWatchlist(security)) {
          ToastUtil.showToast(context, '${security.tradingSymbol} is already in watchlist.', ToastType.error);
          return;
        }
        

        await _watchlistService.addSecurityToWatchlist(
          authState.credentialsModel.clientId.toString(),
          widget.watchlistIndex,
          security,
        );
        
        // Refresh the local watchlist state from storage to ensure sync
        await _refreshWatchlistData();
        ToastUtil.showToast(context, '${security.tradingSymbol} added to watchlist.', ToastType.success);
      } catch (e) {
        ToastUtil.showToast(context, 'Error adding security: $e', ToastType.error);
      }
    }
  }

  void _removeSecurity(SecurityModel security) async {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      try {
        // Remove using symbol to avoid index mismatch issues
        final success = await _watchlistService.removeSecurityFromWatchlistBySymbol(
          authState.credentialsModel.clientId.toString(),
          widget.watchlistIndex,
          security.tradingSymbol,
        );
        
        if (success) {
          // Refresh the local watchlist state from storage to ensure sync
          await _refreshWatchlistData();
          ToastUtil.showToast(context, '${security.tradingSymbol} removed from watchlist.', ToastType.error);
        } else {
          ToastUtil.showToast(context, 'Failed to remove ${security.tradingSymbol}.', ToastType.error);
        }
      } catch (e) {
        ToastUtil.showToast(context, 'Error removing security: $e', ToastType.error);
      }
    }
  }

  Future<void> _refreshWatchlistData() async {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      try {
        final watchlists = await _watchlistService.getWatchlists(authState.credentialsModel.clientId.toString());
        if (mounted && widget.watchlistIndex < watchlists.length) {
          setState(() {
            _currentWatchlist = watchlists[widget.watchlistIndex];
          });
        }
      } catch (e) {
        debugPrint('Error refreshing watchlist data: $e');
      }
    }
  }

  bool _isSecurityInWatchlist(SecurityModel security) {
    if (!mounted || !this.context.mounted || _currentWatchlist == null) return false;
    return _currentWatchlist!.securities.any((s) => s.tradingSymbol == security.tradingSymbol);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _debouncer.dispose();
    super.dispose();
  }

  Future<void> _refreshSecurityList() async {
    SecurityCacheService.refreshSecurityList(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('Add Security', style: TextStyle(color: ThemeConstants.zenWhite)),
        backgroundColor: ThemeConstants.backgroundColor,
        iconTheme: const IconThemeData(color: ThemeConstants.zenWhite),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshSecurityList,
            tooltip: 'Refresh security list',
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomSearchBar(
              controller: _searchController,
              hintText: 'Search for a security...',
              onSearch: (query) {},
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: ToggleButtons(
              isSelected: [_selectedSecurityType == SecurityType.equity, _selectedSecurityType == SecurityType.fno],
              onPressed: (index) {
                setState(() {
                  _selectedSecurityType = index == 0 ? SecurityType.equity : SecurityType.fno;
                  _onSearchChanged();
                });
              },
              color: Colors.white,
              selectedColor: Colors.white,
              fillColor: ThemeConstants.blue,
              borderRadius: BorderRadius.circular(8),
              children: const [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: Text('Equity'),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: Text('F&O'),
                ),
              ],
            ),
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshSecurityList,
              child: BlocBuilder<SecurityListBloc, SecurityListState>(
                builder: (context, state) {
                  if (state is SecurityListLoading) {
                    return const Center(child: CircularLoader());
                  } else if (state is SecurityListError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.error_outline, size: 48, color: Colors.red),
                          const SizedBox(height: 16),
                          Text(
                            'Failed to load securities',
                            style: const TextStyle(color: Colors.white, fontSize: 18),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.error,
                            style: const TextStyle(color: Colors.grey),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _refreshSecurityList,
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  } else if (state is SecurityListLoaded) {
                    if (_searchResults.isEmpty && _searchController.text.isNotEmpty) {
                      return RefreshIndicator(
                        onRefresh: _refreshSecurityList,
                        child: ListView(
                          children: const [
                            SizedBox(height: 200),
                            Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.search_off, size: 48, color: Colors.grey),
                                  SizedBox(height: 16),
                                  Text('No securities found', style: TextStyle(color: Colors.grey)),
                                  SizedBox(height: 8),
                                  Text('Try adjusting your search terms', style: TextStyle(color: Colors.grey, fontSize: 12)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    } else if (_searchController.text.isEmpty) {
                      return RefreshIndicator(
                        onRefresh: _refreshSecurityList,
                        child: ListView(
                          children: const [
                            SizedBox(height: 200),
                            Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.search, size: 48, color: Colors.grey),
                                  SizedBox(height: 16),
                                  Text('Start typing to search for securities', style: TextStyle(color: Colors.grey)),
                                  SizedBox(height: 8),
                                  Text('Pull down to refresh', style: TextStyle(color: Colors.grey, fontSize: 12)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                    return ListView.builder(
                      itemCount: _searchResults.length,
                      itemBuilder: (context, index) {
                        final security = _searchResults[index];
                        final isInWatchlist = _isSecurityInWatchlist(security);
                        final title = _selectedSecurityType == SecurityType.fno
                            ? SecurityListTextFormatter.format(security, 'fno')
                            : security.tradingSymbol;
                        return ListTile(
                          
                          title: Text(title, style: const TextStyle(color: ThemeConstants.zenWhite)),
                          subtitle: Text(security.name, style: TextStyle(color: Colors.grey.shade600)),
                          // leading: (security.instrumentType == 'CE' || security.instrumentType == 'PE')
                          //     ?  security.expiryType == "MONTHLY" ? const Text("Monthly", style: TextStyle(color: Colors.blue)) : const Text("Weekly", style: TextStyle(color: Colors.red))
                          //     : null,
                          trailing: IconButton(
                            icon: isInWatchlist
                                ? const Icon(Icons.remove_circle, color: Colors.red)
                                : const Icon(Icons.add_circle_outline, color: ThemeConstants.blue),
                            onPressed: () => isInWatchlist ? _removeSecurity(security) : _addSecurity(security),
                          ),
                        );
                      },
                    );
                  }
                  return Container();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}



/// debouncer 
/// 
class Debouncer {
  Debouncer({required this.milliseconds});
  final int milliseconds;
  VoidCallback? action;
  Timer? _timer;

  run(VoidCallback action) {
    _timer?.cancel();
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }

  void dispose() {
    _timer?.cancel();
  }
}